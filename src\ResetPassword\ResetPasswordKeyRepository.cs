using ITF.SharedLibraries.MongoDB.Repository;
using MongoDB.Driver;

namespace IT.Microservices.AuthenticationApi.ResetPassword;

public interface IResetPasswordKeyRepository : IMongoRepository<ResetPasswordKeyProjection>
{
    /// <summary>
    /// Find keys by email address
    /// </summary>
    /// <param name="email">User's email address</param>
    /// <returns>Collection of keys for the email</returns>
    Task<IEnumerable<ResetPasswordKeyProjection>> FindByEmailAsync(string email);
}
public class ResetPasswordKeyRepository : MongoRepository<ResetPasswordKeyProjection>, IResetPasswordKeyRepository
{
    public ResetPasswordKeyRepository(IMongoClient mongoClient, ITF.SharedLibraries.MongoDB.Configuration configuration) 
        : base(mongoClient, configuration, "reset_password_keys")
    {
    }



    public async Task<IEnumerable<ResetPasswordKeyProjection>> FindByEmailAsync(string email)
    {
        return await FilterByAsync(key => key.Email == email);
    }
}
