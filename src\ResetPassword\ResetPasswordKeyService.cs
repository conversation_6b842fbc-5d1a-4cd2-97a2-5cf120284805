

namespace IT.Microservices.AuthenticationApi.ResetPassword;

public interface IResetPasswordKeyService
{
    /// <summary>
    /// Generates a secure reset key for the given email address
    /// </summary>
    /// <param name="email">User's email address</param>
    /// <returns>A secure reset key</returns>
    Task<string> GenerateResetKeyAsync(string email);

    /// <summary>
    /// Validates a reset key and returns the associated email if valid
    /// </summary>
    /// <param name="key">The reset key to validate</param>
    /// <returns>Result containing the email if valid, or error if invalid/expired</returns>
    Task<Result<string, string>> ValidateAndGetEmailAsync(string key);

    /// <summary>
    /// Validates a reset key without returning the email (for validation-only purposes)
    /// </summary>
    /// <param name="key">The reset key to validate</param>
    /// <returns>Result indicating if the key is valid or error if invalid/expired</returns>
    Task<Result<bool, string>> ValidateKeyAsync(string key);

    /// <summary>
    /// Invalidates a reset key after successful password reset
    /// </summary>
    /// <param name="key">The reset key to invalidate</param>
    Task InvalidateKeyAsync(string key);
}
public class ResetPasswordKeyService : IResetPasswordKeyService
{
    private readonly ILogger<ResetPasswordKeyService> _logger;
    private readonly IResetPasswordKeyRepository _repository;
    private readonly int _keyExpirationMinutes;

    public ResetPasswordKeyService(
        ILogger<ResetPasswordKeyService> logger,
        IConfiguration configuration,
        IResetPasswordKeyRepository repository)
    {
        _logger = logger;
        _repository = repository;

        // Get expiration time from configuration, default to 15 minutes
        _keyExpirationMinutes = configuration.GetValue<int>("ResetPassword:KeyExpirationMinutes", 15);
    }

    public async Task<string> GenerateResetKeyAsync(string email)
    {
        // Generate a simple UUID as the reset key
        var resetKey = Guid.NewGuid().ToString();

        // Store the key with expiration in MongoDB
        var expirationTime = DateTime.UtcNow.AddMinutes(_keyExpirationMinutes);
        var keyProjection = new ResetPasswordKeyProjection(resetKey, email, expirationTime);

        await _repository.InsertOneAsync(keyProjection);

        _logger.LogInformation("Generated reset key for email {Email}, expires at {Expiration}",
            email, expirationTime);

        return resetKey;
    }

    public async Task<Result<string, string>> ValidateAndGetEmailAsync(string key)
    {
        if (string.IsNullOrWhiteSpace(key))
        {
            return Result.Failure<string, string>("Reset key is null or empty");
        }

        // Check if key exists in MongoDB
        var keyProjection = await _repository.FindByIdAsync(key);
        if (keyProjection == null)
        {
            _logger.LogWarning("Invalid reset key attempted: {Key}", key);
            return Result.Failure<string, string>("Invalid reset key");
        }

        // Check if key has expired or been used
        if (!keyProjection.IsValid())
        {
            if (keyProjection.IsExpired())
            {
                _logger.LogWarning("Expired reset key attempted for email {Email}", keyProjection.Email);
                return Result.Failure<string, string>("Reset key has expired");
            }
            else
            {
                _logger.LogWarning("Already used reset key attempted for email {Email}", keyProjection.Email);
                return Result.Failure<string, string>("Reset key has already been used");
            }
        }

        _logger.LogInformation("Valid reset key used for email {Email}", keyProjection.Email);
        return Result.Success<string, string>(keyProjection.Email);
    }

    public async Task<Result<bool, string>> ValidateKeyAsync(string key)
    {
        if (string.IsNullOrWhiteSpace(key))
        {
            return Result.Failure<bool, string>("Reset key is null or empty");
        }

        // Check if key exists in MongoDB
        var keyProjection = await _repository.FindByIdAsync(key);
        if (keyProjection == null)
        {
            _logger.LogWarning("Invalid reset key attempted: {Key}", key);
            return Result.Failure<bool, string>("Invalid reset key");
        }

        // Check if key has expired or been used
        if (!keyProjection.IsValid())
        {
            if (keyProjection.IsExpired())
            {
                _logger.LogWarning("Expired reset key attempted for email {Email}", keyProjection.Email);
                return Result.Failure<bool, string>("Reset key has expired");
            }
            else
            {
                _logger.LogWarning("Already used reset key attempted for email {Email}", keyProjection.Email);
                return Result.Failure<bool, string>("Reset key has already been used");
            }
        }

        _logger.LogInformation("Valid reset key validated for email {Email}", keyProjection.Email);
        return Result.Success<bool, string>(true);
    }

    public async Task InvalidateKeyAsync(string key)
    {
        var keyProjection = await _repository.FindByIdAsync(key);
        if (keyProjection != null)
        {
            keyProjection.MarkAsUsed();
            await _repository.ReplaceOneAsync(keyProjection);
            _logger.LogInformation("Reset key invalidated for email {Email}", keyProjection.Email);
        }
    }



}
