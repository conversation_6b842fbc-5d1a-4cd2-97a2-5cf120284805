###
# Values for recette environment 
##

# Manual deployment : 
#helmChartPath="/mnt/c/Users/<USER>/Desktop/OCTOPUS/OPS/005-helm-applications/charts/src/IT.Microservices.AbandonedCartEmailSender/"
#helm diff upgrade abandonedcartemailsender ${helmChartPath} --values chart-values/values-recette-italy.yaml -n iti-ms --set 'image.tag=latest,image.repository=itirecetteacr.azurecr.io/abandonedcartemailsender'

replicaCount: 2

image:
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

nameOverride: "abandonedcartemailsender"
fullnameOverride: "abandonedcartemailsender"

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  # Vault policy match ms-* service account
  name: "iti-ms-abandonedcartemailsender"

dotnetProgramName: "IT.Microservices.AbandonedCartEmailSender.dll"
appStartCommand: "dotnet IT.Microservices.AbandonedCartEmailSender.dll"

podAnnotations:
  # vault.hashicorp.com/log-level: 'debug'
  # Inject secret via a file named /vault/secrets/abandonedcartemailsender.pass(k/v format)
  # vault.hashicorp.com/agent-inject: "true"  
  # vault.hashicorp.com/agent-image: 'itfcacheacr.azurecr.io/hashicorp/vault:1.10.3'
  # vault.hashicorp.com/role: "iti-microservices"
  # vault.hashicorp.com/agent-inject-secret-abandonedcartemailsender.pass: "applications/iti-microservices"

  # Inject secret via a configmap named abandonedcartemailsender-secrets(must be defined in Helm Chart)
  # At the moment - the json datas are uploaded to /vault/secrets/ folder 
  # As docker use read-only file system, vault can't inject secrets into /app folder
  # vault.hashicorp.com/agent-inject: 'true'
  # vault.hashicorp.com/agent-configmap: 'abandonedcartemailsender-secrets'
  
  #inject secret via env variables 
  vault.hashicorp.com/agent-inject: 'true'
  vault.hashicorp.com/agent-image: 'itfcacheacr.azurecr.io/hashicorp/vault:1.10.3'
  vault.hashicorp.com/role: 'iti-microservices'
  vault.hashicorp.com/agent-inject-secret-config: 'secret/applications/iti-microservices'
  vault.hashicorp.com/agent-inject-template-config: |
    {{ with secret "applications/iti-microservices" -}}
      export AdobeCampaignEndpoint__Authentication__Credentials__username={{ .Data.adobe_endpoint_user }}
      export AdobeCampaignEndpoint__Authentication__Credentials__password={{ .Data.adobe_endpoint_password }}
      export UNLEASH__KEY={{ .Data.iti_unleash_key }}
    {{- end }}

service:
  type: ClusterIP
  port: 80

ingress:
  hosts: 
  - microservices.recette.interflora.it
  path: "/abandonedcartemailsender"
  tls:
  - hosts:
    - microservices.recette.interflora.it
    secretName: "recette-interflora-it-cert"  
  enabled: true
  ingressClass: "nginx"
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt
    kubernetes.io/tls-acme: "true"
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: nginx/nginx-basic-auth
    nginx.ingress.kubernetes.io/auth-realm: 'Authentication Required'
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/http2-push-preload: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://*.interflora.it"
    nginx.ingress.kubernetes.io/proxy-body-size: 6m
    nginx.ingress.kubernetes.io/configuration-snippet: |-
      ###
      # Whitelist 
      ###
      satisfy any;
      ## MPLS Interflora
      allow ************/32;
      deny all;
      ###
      # Redirections 
      ###
      rewrite ^/abandonedcartemailsender$ /abandonedcartemailsender/swagger/index.html redirect; 

resources: 
  requests:
    cpu: 0.1
    memory: 256Mi

maxUnavailable: "90%"

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 5
  targetCPUUtilizationPercentage: 100
  targetMemoryUtilizationPercentage: 100

nodeSelector: 
  "workload": "itf-fr"

tolerations: []

affinity: {}

additional_config_files: 
  serilogConfig: |-       
    "Serilog": {
      "Using": [],
      "MinimumLevel": {
        "Default": "Information",
        "Override": {
          "Microsoft": "Warning",
          "System": "Information",
          "Elastic": "Warning",
          "Apm": "Warning"
        }
      },
      "WriteTo": [
        {
          "Name": "Console"
        }
      ],
      "Enrich": [
        "FromLogContext",
        "WithMachineName",
        "WithProcessId",
        "WithThreadId"
      ],
      "Properties": {
        "ApplicationName": "iti-abandonedcartemailsender"
      }
    }

  kafkaConfig: |-
    "Kafka": {
      "SubscriberConfigurations": [
        {
          "AutoOffsetReset": 1,
          "ClassName": "AbandonedCartHandler",
          "EnableAutoCommit": false,
          "ManualCommitPeriod": 1,
          "EnablePartitionEof": false,
          "GroupId": "IT.Microservices.AbandonedCartEmailSender",
          "TopicName": "cart",
          "Deserializer": "CommerceTools"
        }
      ]
    }

  adobeCampaignEndpoint: |-
    "AdobeCampaignEndpoint": {
      "Authentication": {
        "Credentials": {
          "soapSettings": {
            "SoapAction": "xtk:session#Logon",
            "XMLRootTokenTag": "LogonResponse",
            "XMLChildTokenTag": "pstrSessionToken"
          }
        },
        "URL": "https://interflora-rt-prod2.campaign.adobe.com/nl/jsp/soaprouter.jsp",
        "AuthMethod": "SOAP",
        "UseExpirationTime": true
      },
      "Url": "https://interflora-rt-prod2.campaign.adobe.com/nl/jsp/soaprouter.jsp",
      "HttpTimeoutInSeconds": 350,
      "PolicyTimeoutInSeconds": 100,
      "HandlerLifetime": 5,
      "DefaultConnectionLimit": 50
    }

env:
  - name: ASPNETCORE_ENVIRONMENT
    value: "recette"

  - name: ASPNETCORE_COUNTRY
    value: "it"

  - name: MongoDb__ConnectionString
    value: "mongodb://iti-ms-mongodb-0.iti-ms-mongodb-headless.iti-ms-common.svc.cluster.local:27017,iti-ms-mongodb-1.iti-ms-mongodb-headless.iti-ms-common.svc.cluster.local:27017,iti-ms-mongodb-2.iti-ms-mongodb-headless.iti-ms-common.svc.cluster.local:27017?replicaSet=rs0"

  - name: MongoDb__DatabaseName
    value: "abandoned_cart"

  - name: MongoDb__CollectionName
    value: "sent_email"

  - name: ElasticApm__ServerUrl
    value: "http://apm-server.logging:8200"

  - name: ElasticApm__ServiceName
    value: "iti-abandonedcartemailsender"    

  - name: ElasticApm__Enabled
    value: "true"

  - name: ElasticApm__TransactionSampleRate
    value: "1"

  - name: ElasticApm__CaptureBody
    value: "all"

  - name: ElasticApm__CaptureHeaders
    value: "true"

  - name: ElasticApm__SpanFramesMinDuration
    value: "0"

  - name: ElasticApm__Environment
    value: "iti-recette"

  - name: ElasticApm__CloudProvider
    value: "none"

  - name: Kafka__BootStrapServers
    value: "iti-ms-kafka-headless.iti-ms-common:9092"

  - name: ElasticSearchLog__ElasticSearchLog
    value: "http://elasticsearch-master-headless.logging:9200"

  - name: Redis__ConnectionString
    value: "iti-ms-redis-headless.iti-ms-common:26379,serviceName=mymaster"

  - name: FeatureFlags__Provider
    value: "featuremanager"

  - name: Unleash__Url
    value: "http://iti-unleash.iti-ms-common:4242/api"

  - name: Unleash__ApplicationName
    value: "iti-abandonedcartemailsender"

  - name: Unleash__FetchTogglesIntervalInSeconds
    value: "15"

  - name: CartAbandonedSettings__DontSendEmailToCartWithActivityOlderThanInMinutes
    value: "1440"
  
  - name: CartAbandonedSettings__AbandonedCartUrl
    value: "https://www.recette.interflora.it/cart/"

  - name: CartAbandonedSettings__VoucherCode
    value: "NONTISCORDARE"
    
  - name: CartAbandonedSettings__InterfloraPlusName
    value: "interflora plus"      
    
  - name: Settings__CountryCode
    value: "IT"

  - name: Unleash__SendMetricsIntervalInSeconds
    value: "30"

  - name: Mock_Enable
    value: "false"

  - name: Mock_MockedEmail
    value: "<EMAIL>"