using FS.Keycloak.RestApiClient.Model;
using IT.SharedLibraries.CT.Customers;
using ITF.SharedLibraries.Keycloak;

namespace IT.Microservices.AuthenticationApi.ResetPassword;

public interface IResetPasswordUseCase
{
    Task<Result<AskResetPasswordResponse, ResetPasswordFailedResponse>> ProcessAskResetPassword(AskResetPasswordRequest req);
    Task<Result<ValidateResetKeyResponse, ResetPasswordFailedResponse>> ProcessValidateResetKey(ValidateResetKeyRequest req);
    Task<Result<ResetPasswordResponse, ResetPasswordFailedResponse>> ProcessResetPassword(ResetPasswordRequest req);
}

public class ResetPasswordUseCase(
    ILogger<ResetPasswordUseCase> logger, 
    IConfiguration conf, 
    IKeycloakLoginHttpService keycloakHttpService, 
    ICustomerService custService, 
    SerializerService serializerService, 
    IKeycloakService keycloakService, 
    IResetPasswordKeyService resetPasswordKeyService,
    ILegacyLoginService? legacyLoginService = null) : IResetPasswordUseCase
{
    public async Task<Result<AskResetPasswordResponse, ResetPasswordFailedResponse>> ProcessAskResetPassword(AskResetPasswordRequest req)
    {
        string errorContent = string.Empty;

        if (string.IsNullOrWhiteSpace(req.Email) || string.IsNullOrWhiteSpace(req.RedirectUrl))
            return new ResetPasswordFailedResponse("InvalidRequestData", $"One or more parameters are invalid : req payload received : {req.Serialize()}");

        var email = req.Email.Trim().ToLower();

        // Check if user exists in Keycloak
        UserRepresentation? user;
        try
        {
            user = await keycloakService.GetKeycloackUserByUsernameOrNullAsync(email);
        }
        catch (Exception e)
        {
            return new ResetPasswordFailedResponse { error = e.GetType().ToString(), error_description = string.IsNullOrEmpty(errorContent) ? $"Exception occurred: {e}" : $"{errorContent} with Exception : {e}", isException = true };
        }

        if (user is null)
        {
            errorContent = $"User with email {email} does not exist in Keycloak";
            logger.LogError(errorContent);
            return new ResetPasswordFailedResponse { error = "UserDoesNotExist", error_description = errorContent };
        }

        // Check if user exists in CommerceTools
        var ctUserExists = await custService.GetByEmail(email);
        if (ctUserExists is null)
        {
            errorContent = $"User with email {email} does not exist in commercetools";
            logger.LogError(errorContent);
            return new ResetPasswordFailedResponse { error = "UserDoesNotExist", error_description = errorContent };
        }

        // Generate secure reset key
        var resetKey = await resetPasswordKeyService.GenerateResetKeyAsync(email);

        // Build redirect URL with key
        var separator = req.RedirectUrl.Contains('?') ? "&" : "?";
        var redirectUrlWithKey = $"{req.RedirectUrl}{separator}code={resetKey}";

        // TODO: Send Kafka message to email reactor microservice
        // await kafkaPublisher.PublishAsync(new PasswordResetRequestedMessage { Email = email, ResetKey = resetKey, RedirectUrl = redirectUrlWithKey });

        logger.LogInformation("Password reset key generated for user {email}", email);

        return new AskResetPasswordResponse(redirectUrlWithKey);
    }

    public async Task<Result<ValidateResetKeyResponse, ResetPasswordFailedResponse>> ProcessValidateResetKey(ValidateResetKeyRequest req)
    {
        if (string.IsNullOrWhiteSpace(req.Key))
            return new ResetPasswordFailedResponse("InvalidRequestData", $"Key parameter is invalid : req payload received : {req.Serialize()}");

        // Validate the reset key
        var keyValidationResult = await resetPasswordKeyService.ValidateKeyAsync(req.Key);
        if (keyValidationResult.IsFailure)
        {
            var errorContent = $"Invalid or expired reset key: {keyValidationResult.Error}";
            logger.LogError(errorContent);
            return new ResetPasswordFailedResponse { error = "InvalidResetKey", error_description = errorContent };
        }

        logger.LogInformation("Reset key validation successful for key {Key}", req.Key);
        return new ValidateResetKeyResponse(true);
    }

    public async Task<Result<ResetPasswordResponse, ResetPasswordFailedResponse>> ProcessResetPassword(ResetPasswordRequest req)
    {
        string errorContent = string.Empty;

        if (string.IsNullOrWhiteSpace(req.Key) || string.IsNullOrWhiteSpace(req.NewPassword))
            return new ResetPasswordFailedResponse("InvalidRequestData", $"One or more parameters are invalid : req payload received : {req.Serialize()}");

        // Validate and get user email from reset key
        var keyValidationResult = await resetPasswordKeyService.ValidateAndGetEmailAsync(req.Key);
        if (keyValidationResult.IsFailure)
        {
            errorContent = $"Invalid or expired reset key: {keyValidationResult.Error}";
            logger.LogError(errorContent);
            return new ResetPasswordFailedResponse { error = "InvalidResetKey", error_description = errorContent };
        }

        var email = keyValidationResult.Value;

        // Get user from Keycloak
        UserRepresentation? user;
        try
        {
            user = await keycloakService.GetKeycloackUserByUsernameOrNullAsync(email);
        }
        catch (Exception e)
        {
            return new ResetPasswordFailedResponse { error = e.GetType().ToString(), error_description = string.IsNullOrEmpty(errorContent) ? $"Exception occurred: {e}" : $"{errorContent} with Exception : {e}", isException = true };
        }

        if (user is null)
        {
            errorContent = $"User with email {email} does not exist in Keycloak";
            logger.LogError(errorContent);
            return new ResetPasswordFailedResponse { error = "UserDoesNotExist", error_description = errorContent };
        }

        // Update password in Keycloak
        try
        {
            var updateRes = await keycloakService.ResetUserPasswordByUserId(user.Id, req.NewPassword);

            if (!updateRes.StatusCode.IsSuccessStatusCode())
            {
                errorContent = $"Error while resetting the user password in Keycloak for email and id : {user.Id} : {email} with the error : {updateRes.ErrorText}";
                logger.LogError(errorContent);
                return new ResetPasswordFailedResponse { error = "ResettingPasswordInKeycloakFailed", error_description = errorContent };
            }
        }
        catch (Exception e)
        {
            return new ResetPasswordFailedResponse { error = "ResettingPasswordInKeycloakFailed", error_description = string.IsNullOrEmpty(errorContent) ? $"Exception occurred: {e}" : $"{errorContent} with Exception : {e}", isException = true };
        }

        // Invalidate the reset key after successful password reset
        await resetPasswordKeyService.InvalidateKeyAsync(req.Key);

        logger.LogInformation("Password successfully reset for user {email}", email);

        return new ResetPasswordResponse(true);
    }
}
