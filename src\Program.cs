using IT.Microservices.AuthenticationApi.ChangePassword;
using IT.Microservices.AuthenticationApi.CreateUser;
using IT.Microservices.AuthenticationApi.DeleteUser;
using IT.Microservices.AuthenticationApi.Login;
using IT.Microservices.AuthenticationApi.Login.FR;
using IT.Microservices.AuthenticationApi.Logout;
using IT.Microservices.AuthenticationApi.ResetPassword;
using IT.SharedLibraries.CT.Customers;
using ITF.SharedLibraries.Keycloak;
using ITF.SharedLibraries.MongoDB.Extensions;

var builder = WebApplication.CreateBuilder(args);

builder.Host.ConfigureHostBuilder(); // Global Inteflora Config on the Host (Logging / Config)

//** Registering Services Part **
builder.Services
    .AddHealthChecksMiddleware()
    .AddAllMetrics()
    .UseFeatureFlags(builder.Configuration)
    .AddSwagger(Assembly.GetExecutingAssembly().GetName().Name!)
    .AddScoped<ILoginUseCase, LoginUseCase>()
    .AddScoped<ILogoutUseCase, LogoutUseCase>()
    .AddScoped<ICreateUserUseCase, CreateUserUseCase>()
    .AddScoped<IChangePasswordUseCase, ChangePasswordUseCase>()
    .AddScoped<IDeleteUserUseCase, DeleteUserUseCase>()
    .AddScoped<IResetPasswordUseCase, ResetPasswordUseCase>()
    .AddSingleton<ICustomerService,CustomerService>()
    .UseMongoDb(builder.Configuration)
    .AddSingleton<IResetPasswordKeyRepository, ResetPasswordKeyRepository>()
    .AddSingleton<IResetPasswordKeyService, ResetPasswordKeyService>();

// Login Token Api
builder.Services.AddHttpClientWithPolicy<IKeycloakLoginHttpService, KeycloakLoginHttpService>(builder.Configuration, "KeycloakLoginEndpoint");

// Full KeycloakService
builder.Services.AddSingleton<IKeycloakService, KeycloakService>();

// Commercetools API
var registry = builder.Services.AddPolicyRegistry();
AddRetryPolicy(registry,
    backoff:
    [
        TimeSpan.FromMilliseconds(20),
        TimeSpan.FromMilliseconds(50),
        TimeSpan.FromMilliseconds(100),
        TimeSpan.FromMilliseconds(250)
    ],
    policyName: "CTRetryPolicy");
AddTimeOutPolicy(registry,
    timeOut: TimeSpan.FromSeconds(15),
    policyName: "CTTimeOut");

builder.Services.UseCommercetoolsApiSerialization();
builder.Services.UseCommercetoolsApi(builder.Configuration, "Client")
    .AddPolicyHandlerFromRegistry("CTRetryPolicy")
    .AddPolicyHandlerFromRegistry("CTTimeOut");

// include optinal Local DI services based on the CT storeKey settings
Setup(builder);

builder.Services
    .AddControllers()
    .UsePascalCase()
    .SuppressAutoACR();

var app = builder.Build();

// ** runtime configure part **
if(app.Environment.IsDevelopment())
    app.UseDeveloperExceptionPage();

app.UseSwaggerEndpoint(app.Services.GetRequiredService<IApiVersionDescriptionProvider>(), "itauthenticationapi");

app.UseRouting();

// Metrics middleware
app.UseAllMetricsMiddleware()
    .UseMiddleware<RequestMiddleware>();

app.UseAuthorization();

app.UseHealthChecks();
app.UseReadynessRoute();
app.MapMetrics();
app.MapControllers();

await app.RunAsync();

static void Setup(WebApplicationBuilder builder)
{
    switch (builder.Configuration?.GetSection("Client:StoreProjectionKey").Value)
    {
        case "ITF":
            builder.Services.AddHttpClientWithPolicy<IHybrisHttpService, HybrisHttpService>(builder.Configuration, "HybrisEndpoint");
            builder.Services.AddSingleton<ILegacyLoginService, ItfLegacyService>();
            break;
        case "ITI":
            break;
        case "ITE":
            break;
        case "ITP":
            break;
        case "ITD":
            break;
        case "ITS":
            break;
        default:
            throw new NotImplementedException($"No CT StoreKey valid found in the settings : {builder.Configuration?.GetSection("Client:StoreProjectionKey").Value} ");
    }
}

