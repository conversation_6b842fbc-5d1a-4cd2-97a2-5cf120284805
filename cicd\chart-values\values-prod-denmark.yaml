###
# Values for prod environment 
##

# Manual deployment : 
#helmChartPath="/mnt/c/Users/<USER>/Desktop/OCTOPUS/OPS/005-helm-applications/charts/src/IT.Microservices.AuthenticationApi/"
#helm diff upgrade itauthenticationapi ${helmChartPath} --values chart-values/values-prod-denmark.yaml -n itd-ms --set 'image.tag=latest,image.repository=itdprodacr.azurecr.io/itauthenticationapi'

replicaCount: 2

image:
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

nameOverride: "itauthenticationapi"
fullnameOverride: "itauthenticationapi"

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  # Vault policy match ms-* service account
  name: "itd-ms-itauthenticationapi"

dotnetProgramName: "IT.Microservices.AuthenticationApi.dll"

podAnnotations: []
  # vault.hashicorp.com/log-level: 'debug'
  # Inject secret via a file named /vault/secrets/itauthenticationapi.pass(k/v format)
  # vault.hashicorp.com/agent-inject: "true"
  # vault.hashicorp.com/agent-image: 'itfcacheacr.azurecr.io/hashicorp/vault:1.10.3'
  # vault.hashicorp.com/role: "itd-microservices"
  # vault.hashicorp.com/agent-inject-secret-itauthenticationapi.pass: "applications/itd-microservices"

  # Inject secret via a configmap named itauthenticationapi-secrets(must be defined in Helm Chart)
  # At the moment - the json datas are uploaded to /vault/secrets/ folder 
  # As docker use read-only file system, vault can't inject secrets into /app folder
  # vault.hashicorp.com/agent-inject: 'true'
  # vault.hashicorp.com/agent-configmap: 'itauthenticationapi-secrets'
  
  #inject secret via env variables 
  #vault.hashicorp.com/agent-inject: 'true'
  #vault.hashicorp.com/role: 'itd-microservices'
  #vault.hashicorp.com/agent-inject-secret-config: 'secret/applications/itd-microservices'
  #vault.hashicorp.com/agent-inject-template-config: |

service:
  type: ClusterIP
  port: 80

ingress:
  hosts: 
  - microservices.prod.interflora.dk
  path: "/itauthenticationapi"
  enabled: true
  ingressClass: "nginx"
  annotations:
    nginx.ingress.kubernetes.io/auth-type: basic
    nginx.ingress.kubernetes.io/auth-secret: nginx/nginx-basic-auth
    nginx.ingress.kubernetes.io/auth-realm: 'Authentication Required'
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/http2-push-preload: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://*.interflora.dk"
    nginx.ingress.kubernetes.io/proxy-body-size: 6m
    nginx.ingress.kubernetes.io/configuration-snippet: |-
      ###
      # Whitelist 
      ###
      satisfy any;
      ## MPLS Interflora
      allow *************/32;
      allow *************/32;
      allow ***************/32;
      deny all;
      ###
      # Redirections 
      ###
      rewrite ^/itauthenticationapi$ /itauthenticationapi/swagger/index.html redirect; 

resources: 
  requests:
    cpu: 0.1
    memory: 256Mi

maxUnavailable: "50%"

livenessProbe:
  httpGet:
    path: /health
    port: 80
  initialDelaySeconds: 0
  periodSeconds: 10
  timeoutSeconds: 1
  failureThreshold: 3
  
autoscaling:
  enabled: true
  minReplicas: 2
  maxReplicas: 10
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

nodeSelector: 
  "workload": "itf-fr"

tolerations: []

affinity: {}

additional_config_files: 
  serilogConfig: |-       
    "Serilog": {
      "Using": [],
      "MinimumLevel": {
        "Default": "Information",
        "Override": {
          "Microsoft": "Warning",
          "System": "Information",
          "Elastic": "Warning",
          "Apm": "Warning"
        }
      },
      "WriteTo": [
        {
          "Name": "Console"
        }
      ],
      "Enrich": [
        "FromLogContext",
        "WithMachineName",
        "WithProcessId",
        "WithThreadId"
      ],
      "Properties": {
        "ApplicationName": "itd-itauthenticationapi"
      }
    }

env:
  - name: ASPNETCORE_ENVIRONMENT
    value: "prod"

  - name: ASPNETCORE_COUNTRY
    value: "dk"

  - name: MongoDb__ConnectionString
    value: "mongodb://itd-ms-mongodb-0.itd-ms-mongodb-headless.itd-ms-common.svc.cluster.local:27017,itd-ms-mongodb-1.itd-ms-mongodb-headless.itd-ms-common.svc.cluster.local:27017,itd-ms-mongodb-2.itd-ms-mongodb-headless.itd-ms-common.svc.cluster.local:27017?replicaSet=rs0"

  - name: MongoDb__DatabaseName
    value: "authentication_api"

  - name: MongoDb__CollectionName
    value: "reset_password_keys"

  - name: MongoDb__TtlFieldName
    value: "ExpirationTime"

  - name: MongoDb__TtlExpirationSeconds
    value: "3600"

  - name: MongoDb__TtlIndexCreationOnExistingCollection
    value: "true"

  - name: MongoDb__ForceTtlIndexCreation
    value: "false"

  - name: ResetPassword__KeyExpirationMinutes
    value: "15"

  - name: ElasticApm__Environment
    value: "itd-prod"

  - name: ElasticApm__ServerUrl
    value: "http://apm-server.logging:8200"

  - name: ElasticApm__ServiceName
    value: "itd-itauthenticationapi"

  - name: ElasticApm__Enabled
    value: "true"

  - name: ElasticApm__TransactionSampleRate
    value: "1"

  - name: ElasticApm__CaptureBody
    value: "all"

  - name: ElasticApm__CaptureHeaders
    value: "true"

  - name: ElasticApm__SpanFramesMinDuration
    value: "0"

  - name: ElasticApm__CloudProvider
    value: "none"

  - name: Kafka__BootStrapServers
    value: "itd-ms-kafka-headless.itd-ms-common:9092"

  - name: ElasticSearchLog__ElasticSearchLog
    value: "http://elasticsearch-master-headless.logging:9200"

  - name: Redis__ConnectionString
    value: "itd-ms-redis-headless.itd-ms-common:26379,serviceName=mymaster"

  - name: FeatureFlags__Provider
    value: "featuremanager"
  
  - name: Unleash__Url
    value: "http://itd-unleash.itd-ms-common:4242/api"

  - name: Unleash__ApplicationName
    value: "itd-itauthenticationapi"

  - name: Unleash__FetchTogglesIntervalInSeconds
    value: "15"

  - name: Unleash__SendMetricsIntervalInSeconds
    value: "30"

  - name: Unleash__ProjectId
    value: "default"

  - name: Unleash__Environment
    value: "production"