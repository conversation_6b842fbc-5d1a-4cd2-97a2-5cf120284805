using IT.Microservices.AuthenticationApi.Login;
using IT.Microservices.AuthenticationApi.Logout;
using IT.Microservices.AuthenticationApi.CreateUser;
using IT.Microservices.AuthenticationApi.ChangePassword;
using IT.Microservices.AuthenticationApi.DeleteUser;
using IT.Microservices.AuthenticationApi.ResetPassword;
using IT.Microservices.AuthenticationApi.Common;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using System.Net;
using System.Text;
using System.Text.Json;
using System.Text.Json.Nodes;
using CSharpFunctionalExtensions;
using ITF.Lib.Common.Error;

namespace IT.Microservices.AuthenticationApi.UnitTests;

#region Login Controller Tests

public class LoginControllerTests
{
    private readonly Mock<ILogger<LoginController>> _mockLogger;
    private readonly Mock<ILoginUseCase> _mockLoginUseCase;
    private readonly LoginController _controller;

    public LoginControllerTests()
    {
        _mockLogger = new Mock<ILogger<LoginController>>();
        _mockLoginUseCase = new Mock<ILoginUseCase>();
        _controller = new LoginController(_mockLogger.Object, _mockLoginUseCase.Object);
    }

    [Fact]
    public async Task LoginAsync_ValidRequest_ReturnsOkResult()
    {
        // Arrange
        var request = new LoginRequest("<EMAIL>", "password123");
        var expectedResult = new AuthenticationResponse(
            "access_token_value",
            3600,
            7200,
            "refresh_token_value",
            "Bearer",
            "id_token_value",
            0,
            "session_state_value",
            "openid");
        _mockLoginUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Success<AuthenticationResponse, AuthenticationFailedResponse>(expectedResult));

        // Act
        var result = await _controller.LoginAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.Equal(expectedResult, okResult.Value);
    }

    [Fact]
    public async Task LoginAsync_NullRequest_ReturnsBadRequest()
    {
        // Act
        var result = await _controller.LoginAsync(null!);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        Assert.NotNull(badRequestResult.Value);
        Assert.IsType<Error>(badRequestResult.Value);
    }

    [Fact]
    public async Task LoginAsync_UseCaseFailure_ReturnsOkWithError()
    {
        // Arrange
        var request = new LoginRequest("<EMAIL>", "wrongpassword");
        var errorResponse = new AuthenticationFailedResponse("invalid_credentials", "Invalid credentials");
        _mockLoginUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Failure<AuthenticationResponse, AuthenticationFailedResponse>(errorResponse));

        // Act
        var result = await _controller.LoginAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.Equal(errorResponse, okResult.Value);
    }

    [Fact]
    public async Task IntrospectionTokenAsync_ValidRequest_ReturnsOkResult()
    {
        // Arrange
        var request = new IntrospectionRequest("valid-access-token");
        var expectedResult = new IntrospectionResponse(true, "<EMAIL>", "<EMAIL>", "user-id", 1234567890, 1234567890, "client-id", "openid");
        _mockLoginUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Success<IntrospectionResponse, AuthenticationFailedResponse>(expectedResult));

        // Act
        var result = await _controller.IntrospectionTokenAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.Equal(expectedResult, okResult.Value);
    }

    [Fact]
    public async Task IntrospectionTokenAsync_NullRequest_ReturnsBadRequest()
    {
        // Act
        var result = await _controller.IntrospectionTokenAsync(null!);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        Assert.NotNull(badRequestResult.Value);
        Assert.IsType<Error>(badRequestResult.Value);
    }

    [Fact]
    public async Task RefreshTokenAsync_ValidRequest_ReturnsOkResult()
    {
        // Arrange
        var request = new RefreshTokenRequest("valid-refresh-token");
        var expectedResponse = new AuthenticationResponse(
            access_token: "new-access-token",
            expires_in: 300,
            refresh_expires_in: 1800,
            refresh_token: "new-refresh-token",
            token_type: "Bearer",
            id_token: "new-id-token",
            notbeforepolicy: 0,
            session_state: "session-123",
            scope: "openid"
        );
        _mockLoginUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Success<AuthenticationResponse, AuthenticationFailedResponse>(expectedResponse));

        // Act
        var result = await _controller.RefreshTokenAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.Equal(expectedResponse, okResult.Value);
    }

    [Fact]
    public async Task RefreshTokenAsync_NullRequest_ReturnsBadRequest()
    {
        // Act
        var result = await _controller.RefreshTokenAsync(null!);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        Assert.NotNull(badRequestResult.Value);
        Assert.IsType<Error>(badRequestResult.Value);
    }
}

#endregion

#region Request Model Tests

public class LoginRequestTests
{
    [Fact]
    public void LoginRequest_ValidData_CreatesCorrectly()
    {
        // Arrange & Act
        var request = new LoginRequest("<EMAIL>", "password123");

        // Assert
        Assert.Equal("<EMAIL>", request.Email);
        Assert.Equal("password123", request.Password);
    }

    [Fact]
    public void LoginRequest_EmptyEmail_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new LoginRequest("", "password123");

        // Assert
        Assert.Equal("", request.Email);
    }

    [Fact]
    public void LoginRequest_NullPassword_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new LoginRequest("<EMAIL>", null!);

        // Assert
        Assert.Null(request.Password);
    }
}

public class IntrospectionRequestTests
{
    [Fact]
    public void IntrospectionRequest_ValidToken_CreatesCorrectly()
    {
        // Arrange & Act
        var request = new IntrospectionRequest("valid-access-token");

        // Assert
        Assert.Equal("valid-access-token", request.AccessToken);
    }

    [Fact]
    public void IntrospectionRequest_EmptyToken_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new IntrospectionRequest("");

        // Assert
        Assert.Equal("", request.AccessToken);
    }

    [Fact]
    public void IntrospectionRequest_NullToken_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new IntrospectionRequest(null!);

        // Assert
        Assert.Null(request.AccessToken);
    }
}

public class RefreshTokenRequestTests
{
    [Fact]
    public void RefreshTokenRequest_ValidToken_CreatesCorrectly()
    {
        // Arrange & Act
        var request = new RefreshTokenRequest("valid-refresh-token");

        // Assert
        Assert.Equal("valid-refresh-token", request.RefreshToken);
    }

    [Fact]
    public void RefreshTokenRequest_EmptyRefreshToken_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new RefreshTokenRequest("");

        // Assert
        Assert.Equal("", request.RefreshToken);
    }

    [Fact]
    public void RefreshTokenRequest_NullRefreshToken_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new RefreshTokenRequest(null!);

        // Assert
        Assert.Null(request.RefreshToken);
    }
}

#endregion

#region Logout Controller Tests

public class LogoutControllerTests
{
    private readonly Mock<ILogger<LogoutController>> _mockLogger;
    private readonly Mock<ILogoutUseCase> _mockLogoutUseCase;
    private readonly LogoutController _controller;

    public LogoutControllerTests()
    {
        _mockLogger = new Mock<ILogger<LogoutController>>();
        _mockLogoutUseCase = new Mock<ILogoutUseCase>();
        _controller = new LogoutController(_mockLogger.Object, _mockLogoutUseCase.Object);
    }

    [Fact]
    public async Task RevokeTokensAsync_ValidRequest_ReturnsOkResult()
    {
        // Arrange
        var request = new RevokeTokensRequest("<EMAIL>", "access-token", "refresh-token");
        _mockLogoutUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Success<bool, LogoutFailedResponse>(true));

        // Act
        var result = await _controller.RevokeTokensAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task RevokeTokensAsync_NullRequest_ReturnsBadRequest()
    {
        // Act
        var result = await _controller.RevokeTokensAsync(null!);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        Assert.NotNull(badRequestResult.Value);
        Assert.IsType<Error>(badRequestResult.Value);
    }

    [Fact]
    public async Task RevokeTokensAsync_UseCaseFailure_ReturnsOkWithError()
    {
        // Arrange
        var request = new RevokeTokensRequest("<EMAIL>", "invalid-token", "invalid-refresh");
        var errorResponse = new LogoutFailedResponse("token_revocation_failed", "Failed to revoke tokens");
        _mockLogoutUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Failure<bool, LogoutFailedResponse>(errorResponse));

        // Act
        var result = await _controller.RevokeTokensAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.Equal(errorResponse, okResult.Value);
    }

    [Fact]
    public async Task LogoutAsync_ValidRequest_ReturnsOkResult()
    {
        // Arrange
        var request = new LogoutRequest("<EMAIL>", "access-token", "refresh-token");
        _mockLogoutUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Success<bool, LogoutFailedResponse>(true));

        // Act
        var result = await _controller.LogoutAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task LogoutAsync_NullRequest_ReturnsBadRequest()
    {
        // Act
        var result = await _controller.LogoutAsync(null!);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        Assert.NotNull(badRequestResult.Value);
        Assert.IsType<Error>(badRequestResult.Value);
    }
}

public class LogoutRequestTests
{
    [Fact]
    public void RevokeTokensRequest_ValidData_CreatesCorrectly()
    {
        // Arrange & Act
        var request = new RevokeTokensRequest("<EMAIL>", "access-token", "refresh-token");

        // Assert
        Assert.Equal("<EMAIL>", request.Email);
        Assert.Equal("access-token", request.AccessToken);
        Assert.Equal("refresh-token", request.RefreshToken);
    }

    [Fact]
    public void LogoutRequest_ValidData_CreatesCorrectly()
    {
        // Arrange & Act
        var request = new LogoutRequest("<EMAIL>", "access-token", "refresh-token");

        // Assert
        Assert.Equal("<EMAIL>", request.Email);
        Assert.Equal("access-token", request.AccessToken);
        Assert.Equal("refresh-token", request.RefreshToken);
    }

    [Fact]
    public void RevokeTokensRequest_EmptyEmail_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new RevokeTokensRequest("", "access-token", "refresh-token");

        // Assert
        Assert.Equal("", request.Email);
    }

    [Fact]
    public void LogoutRequest_NullTokens_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new LogoutRequest("<EMAIL>", null!, null!);

        // Assert
        Assert.Null(request.AccessToken);
        Assert.Null(request.RefreshToken);
    }
}

#endregion

#region CreateUser Controller Tests

public class CreateUserControllerTests
{
    private readonly Mock<ILogger<CreateUserController>> _mockLogger;
    private readonly Mock<ICreateUserUseCase> _mockCreateUserUseCase;
    private readonly CreateUserController _controller;

    public CreateUserControllerTests()
    {
        _mockLogger = new Mock<ILogger<CreateUserController>>();
        _mockCreateUserUseCase = new Mock<ICreateUserUseCase>();
        _controller = new CreateUserController(_mockLogger.Object, _mockCreateUserUseCase.Object);
    }

    [Fact]
    public async Task CreateUserAsync_ValidRequest_ReturnsOkResult()
    {
        // Arrange
        var request = new CreateUserRequest("<EMAIL>", "password123");
        var expectedResult = new JsonObject { ["userId"] = "12345", ["success"] = true };
        _mockCreateUserUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Success<JsonObject, CreateUserFailedResponse>(expectedResult));

        // Act
        var result = await _controller.CreateUserAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.Equal(expectedResult, okResult.Value);
    }

    [Fact]
    public async Task CreateUserAsync_NullRequest_ReturnsBadRequest()
    {
        // Act
        var result = await _controller.CreateUserAsync(null!);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        Assert.NotNull(badRequestResult.Value);
        Assert.IsType<Error>(badRequestResult.Value);
    }

    [Fact]
    public async Task CreateUserAsync_UseCaseFailure_ReturnsOkWithError()
    {
        // Arrange
        var request = new CreateUserRequest("<EMAIL>", "password123");
        var errorResponse = new CreateUserFailedResponse("user_already_exists", "User already exists");
        _mockCreateUserUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Failure<JsonObject, CreateUserFailedResponse>(errorResponse));

        // Act
        var result = await _controller.CreateUserAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.Equal(errorResponse, okResult.Value);
    }
}

public class CreateUserRequestTests
{
    [Fact]
    public void CreateUserRequest_ValidData_CreatesCorrectly()
    {
        // Arrange & Act
        var request = new CreateUserRequest("<EMAIL>", "password123");

        // Assert
        Assert.Equal("<EMAIL>", request.Email);
        Assert.Equal("password123", request.Password);
    }

    [Fact]
    public void CreateUserRequest_EmptyEmail_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new CreateUserRequest("", "password123");

        // Assert
        Assert.Equal("", request.Email);
    }

    [Fact]
    public void CreateUserRequest_NullPassword_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new CreateUserRequest("<EMAIL>", null!);

        // Assert
        Assert.Null(request.Password);
    }

    [Fact]
    public void CreateUserRequest_WeakPassword_ShouldStillCreate()
    {
        // Arrange & Act
        var request = new CreateUserRequest("<EMAIL>", "123");

        // Assert
        Assert.Equal("<EMAIL>", request.Email);
        Assert.Equal("123", request.Password);
    }
}

#endregion

#region ChangePassword Controller Tests

public class ChangePasswordControllerTests
{
    private readonly Mock<ILogger<ChangePasswordController>> _mockLogger;
    private readonly Mock<IChangePasswordUseCase> _mockChangePasswordUseCase;
    private readonly ChangePasswordController _controller;

    public ChangePasswordControllerTests()
    {
        _mockLogger = new Mock<ILogger<ChangePasswordController>>();
        _mockChangePasswordUseCase = new Mock<IChangePasswordUseCase>();
        _controller = new ChangePasswordController(_mockLogger.Object, _mockChangePasswordUseCase.Object);
    }

    [Fact]
    public async Task ChangePasswordAsync_ValidRequest_ReturnsOkResult()
    {
        // Arrange
        var request = new ChangePasswordRequest("eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...", "oldPassword123", "newPassword456");
        _mockChangePasswordUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Success<bool, ChangePasswordFailedResponse>(true));

        // Act
        var result = await _controller.ChangePasswordAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task ChangePasswordAsync_NullRequest_ReturnsBadRequest()
    {
        // Act
        var result = await _controller.ChangePasswordAsync(null!);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        Assert.NotNull(badRequestResult.Value);
        Assert.IsType<Error>(badRequestResult.Value);
    }

    [Fact]
    public async Task ChangePasswordAsync_UseCaseFailure_ReturnsOkWithError()
    {
        // Arrange
        var request = new ChangePasswordRequest("eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...", "wrongOldPassword", "newPassword456");
        var errorResponse = new ChangePasswordFailedResponse("invalid_old_password", "Old password is incorrect");
        _mockChangePasswordUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Failure<bool, ChangePasswordFailedResponse>(errorResponse));

        // Act
        var result = await _controller.ChangePasswordAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.Equal(errorResponse, okResult.Value);
    }

    [Fact]
    public async Task ChangePasswordAsync_UserNotFound_ReturnsOkWithError()
    {
        // Arrange
        var request = new ChangePasswordRequest("eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...", "oldPassword123", "newPassword456");
        var errorResponse = new ChangePasswordFailedResponse("UserDoesNotExist", "User <NAME_EMAIL> does not exist in Keycloak");
        _mockChangePasswordUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Failure<bool, ChangePasswordFailedResponse>(errorResponse));

        // Act
        var result = await _controller.ChangePasswordAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.Equal(errorResponse, okResult.Value);
    }
}

public class ChangePasswordRequestTests
{
    [Fact]
    public void ChangePasswordRequest_ValidData_CreatesCorrectly()
    {
        // Arrange & Act
        var request = new ChangePasswordRequest("eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...", "oldPassword123", "newPassword456");

        // Assert
        Assert.Equal("eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...", request.AccessToken);
        Assert.Equal("oldPassword123", request.OldPassword);
        Assert.Equal("newPassword456", request.NewPassword);
    }

    [Fact]
    public void ChangePasswordRequest_EmptyAccessToken_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new ChangePasswordRequest("", "oldPassword123", "newPassword456");

        // Assert
        Assert.Equal("", request.AccessToken);
    }

    [Fact]
    public void ChangePasswordRequest_NullPasswords_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new ChangePasswordRequest("eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...", null!, null!);

        // Assert
        Assert.Null(request.OldPassword);
        Assert.Null(request.NewPassword);
    }

    [Fact]
    public void ChangePasswordRequest_SamePasswords_ShouldStillCreate()
    {
        // Arrange & Act
        var request = new ChangePasswordRequest("eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...", "password123", "password123");

        // Assert
        Assert.Equal("password123", request.OldPassword);
        Assert.Equal("password123", request.NewPassword);
    }

    [Fact]
    public void ChangePasswordRequest_EmptyNewPassword_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new ChangePasswordRequest("eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...", "oldPassword123", "");

        // Assert
        Assert.Equal("", request.NewPassword);
    }

    [Fact]
    public void ChangePasswordRequest_NullAccessToken_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new ChangePasswordRequest(null!, "oldPassword123", "newPassword456");

        // Assert
        Assert.Null(request.AccessToken);
    }
}

#endregion

#region ResetPassword Controller Tests

public class ResetPasswordControllerTests
{
    private readonly Mock<ILogger<ResetPasswordController>> _mockLogger;
    private readonly Mock<IResetPasswordUseCase> _mockResetPasswordUseCase;
    private readonly ResetPasswordController _controller;

    public ResetPasswordControllerTests()
    {
        _mockLogger = new Mock<ILogger<ResetPasswordController>>();
        _mockResetPasswordUseCase = new Mock<IResetPasswordUseCase>();
        _controller = new ResetPasswordController(_mockLogger.Object, _mockResetPasswordUseCase.Object);
    }

    [Fact]
    public async Task AskResetPasswordAsync_ValidRequest_ReturnsOkResult()
    {
        // Arrange
        var request = new AskResetPasswordRequest("<EMAIL>", "https://app.com/reset");
        var response = new AskResetPasswordResponse("https://app.com/reset?resetKey=abc123");
        _mockResetPasswordUseCase.Setup(x => x.ProcessAskResetPassword(request))
            .ReturnsAsync(Result.Success<AskResetPasswordResponse, ResetPasswordFailedResponse>(response));

        // Act
        var result = await _controller.AskResetPasswordAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.Equal(response, okResult.Value);
    }

    [Fact]
    public async Task AskResetPasswordAsync_NullRequest_ReturnsBadRequest()
    {
        // Act
        var result = await _controller.AskResetPasswordAsync(null!);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        Assert.NotNull(badRequestResult.Value);
        Assert.IsType<Error>(badRequestResult.Value);
    }

    [Fact]
    public async Task ResetPasswordAsync_ValidRequest_ReturnsOkResult()
    {
        // Arrange
        var request = new ResetPasswordRequest("abc123", "newPassword123");
        var response = new ResetPasswordResponse(true);
        _mockResetPasswordUseCase.Setup(x => x.ProcessResetPassword(request))
            .ReturnsAsync(Result.Success<ResetPasswordResponse, ResetPasswordFailedResponse>(response));

        // Act
        var result = await _controller.ResetPasswordAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.Equal(response, okResult.Value);
    }

    [Fact]
    public async Task ResetPasswordAsync_NullRequest_ReturnsBadRequest()
    {
        // Act
        var result = await _controller.ResetPasswordAsync(null!);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        Assert.NotNull(badRequestResult.Value);
        Assert.IsType<Error>(badRequestResult.Value);
    }
}

#endregion

#region ResetPassword Request Model Tests

public class AskResetPasswordRequestTests
{
    [Fact]
    public void AskResetPasswordRequest_ValidData_CreatesCorrectly()
    {
        // Arrange & Act
        var request = new AskResetPasswordRequest("<EMAIL>", "https://app.com/reset");

        // Assert
        Assert.Equal("<EMAIL>", request.Email);
        Assert.Equal("https://app.com/reset", request.RedirectUrl);
    }

    [Fact]
    public void AskResetPasswordRequest_EmptyEmail_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new AskResetPasswordRequest("", "https://app.com/reset");

        // Assert
        Assert.Equal("", request.Email);
    }

    [Fact]
    public void AskResetPasswordRequest_NullRedirectUrl_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new AskResetPasswordRequest("<EMAIL>", null!);

        // Assert
        Assert.Null(request.RedirectUrl);
    }
}

public class ResetPasswordRequestTests
{
    [Fact]
    public void ResetPasswordRequest_ValidData_CreatesCorrectly()
    {
        // Arrange & Act
        var request = new ResetPasswordRequest("abc123", "newPassword123");

        // Assert
        Assert.Equal("abc123", request.Key);
        Assert.Equal("newPassword123", request.NewPassword);
    }

    [Fact]
    public void ResetPasswordRequest_EmptyKey_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new ResetPasswordRequest("", "newPassword123");

        // Assert
        Assert.Equal("", request.Key);
    }

    [Fact]
    public void ResetPasswordRequest_NullNewPassword_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new ResetPasswordRequest("abc123", null!);

        // Assert
        Assert.Null(request.NewPassword);
    }
}

#endregion

#region ResetPasswordKeyService Tests

public class ResetPasswordKeyServiceTests
{
    private readonly Mock<ILogger<ResetPasswordKeyService>> _mockLogger;
    private readonly Mock<IConfiguration> _mockConfiguration;
    private readonly Mock<IResetPasswordKeyRepository> _mockRepository;
    private readonly ResetPasswordKeyService _service;

    public ResetPasswordKeyServiceTests()
    {
        _mockLogger = new Mock<ILogger<ResetPasswordKeyService>>();
        _mockConfiguration = new Mock<IConfiguration>();
        _mockRepository = new Mock<IResetPasswordKeyRepository>();

        // Setup configuration mock using IConfigurationSection
        var mockSection = new Mock<IConfigurationSection>();
        mockSection.Setup(x => x.Value).Returns("15");
        _mockConfiguration.Setup(x => x.GetSection("ResetPassword:KeyExpirationMinutes"))
            .Returns(mockSection.Object);

        _service = new ResetPasswordKeyService(_mockLogger.Object, _mockConfiguration.Object, _mockRepository.Object);
    }

    [Fact]
    public async Task GenerateResetKeyAsync_ValidEmail_ReturnsKey()
    {
        // Arrange
        var email = "<EMAIL>";
        _mockRepository.Setup(x => x.InsertOneAsync(It.IsAny<ResetPasswordKeyProjection>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _service.GenerateResetKeyAsync(email);

        // Assert
        Assert.NotNull(result);
        Assert.NotEmpty(result);
        Assert.True(Guid.TryParse(result, out _)); // Should be a valid GUID
        _mockRepository.Verify(x => x.InsertOneAsync(It.Is<ResetPasswordKeyProjection>(
            p => p.Email == email && p.ExpirationTime > DateTime.UtcNow)), Times.Once);
    }

    [Fact]
    public async Task ValidateAndGetEmailAsync_ValidKey_ReturnsSuccess()
    {
        // Arrange
        var email = "<EMAIL>";
        var key = Guid.NewGuid().ToString();
        var keyProjection = new ResetPasswordKeyProjection(key, email, DateTime.UtcNow.AddMinutes(15));

        _mockRepository.Setup(x => x.FindByIdAsync(key))
            .ReturnsAsync(keyProjection);

        // Act
        var result = await _service.ValidateAndGetEmailAsync(key);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal(email, result.Value);
    }

    [Fact]
    public async Task GenerateResetKeyAsync_ValidEmail_CreatesCorrectFormat()
    {
        // Arrange
        var email = "<EMAIL>";

        _mockRepository.Setup(x => x.InsertOneAsync(It.IsAny<ResetPasswordKeyProjection>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _service.GenerateResetKeyAsync(email);

        // Assert
        Assert.NotNull(result);
        Assert.NotEmpty(result);
        Assert.True(Guid.TryParse(result, out _)); // Should be a valid GUID

        // Verify repository was called
        _mockRepository.Verify(x => x.InsertOneAsync(It.IsAny<ResetPasswordKeyProjection>()), Times.Once);
    }

    [Fact]
    public async Task ValidateAndGetEmailAsync_ExpiredKey_ReturnsFailure()
    {
        // Arrange
        var email = "<EMAIL>";
        var key = Guid.NewGuid().ToString();
        var keyProjection = new ResetPasswordKeyProjection(key, email, DateTime.UtcNow.AddMinutes(-5)); // Expired

        _mockRepository.Setup(x => x.FindByIdAsync(key))
            .ReturnsAsync(keyProjection);

        // Act
        var result = await _service.ValidateAndGetEmailAsync(key);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("expired", result.Error);
    }

    [Fact]
    public async Task ValidateAndGetEmailAsync_NonExistentKey_ReturnsFailure()
    {
        // Arrange
        var key = "nonexistent_key";
        _mockRepository.Setup(x => x.FindByIdAsync(key))
            .ReturnsAsync((ResetPasswordKeyProjection)null!);

        // Act
        var result = await _service.ValidateAndGetEmailAsync(key);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("Invalid reset key", result.Error);
    }

    [Fact]
    public async Task InvalidateKeyAsync_ValidKey_MarksAsUsed()
    {
        // Arrange
        var email = "<EMAIL>";
        var key = Guid.NewGuid().ToString();
        var keyProjection = new ResetPasswordKeyProjection(key, email, DateTime.UtcNow.AddMinutes(15));

        _mockRepository.Setup(x => x.FindByIdAsync(key))
            .ReturnsAsync(keyProjection);
        _mockRepository.Setup(x => x.ReplaceOneAsync(It.IsAny<ResetPasswordKeyProjection>(), true))
            .Returns(Task.CompletedTask);

        // Act
        await _service.InvalidateKeyAsync(key);

        // Assert
        _mockRepository.Verify(x => x.ReplaceOneAsync(It.Is<ResetPasswordKeyProjection>(
            p => p.Id == key && p.IsUsed), true), Times.Once);
    }

    [Fact]
    public async Task ValidateKeyAsync_ValidKey_ReturnsSuccess()
    {
        // Arrange
        var email = "<EMAIL>";
        var key = Guid.NewGuid().ToString();
        var keyProjection = new ResetPasswordKeyProjection(key, email, DateTime.UtcNow.AddMinutes(15));

        _mockRepository.Setup(x => x.FindByIdAsync(key))
            .ReturnsAsync(keyProjection);

        // Act
        var result = await _service.ValidateKeyAsync(key);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.True(result.Value);
    }

    [Fact]
    public async Task ValidateKeyAsync_ExpiredKey_ReturnsFailure()
    {
        // Arrange
        var email = "<EMAIL>";
        var key = Guid.NewGuid().ToString();
        var keyProjection = new ResetPasswordKeyProjection(key, email, DateTime.UtcNow.AddMinutes(-5)); // Expired

        _mockRepository.Setup(x => x.FindByIdAsync(key))
            .ReturnsAsync(keyProjection);

        // Act
        var result = await _service.ValidateKeyAsync(key);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("expired", result.Error);
    }

    [Fact]
    public async Task ValidateKeyAsync_UsedKey_ReturnsFailure()
    {
        // Arrange
        var email = "<EMAIL>";
        var key = Guid.NewGuid().ToString();
        var keyProjection = new ResetPasswordKeyProjection(key, email, DateTime.UtcNow.AddMinutes(15));
        keyProjection.MarkAsUsed(); // Mark as used

        _mockRepository.Setup(x => x.FindByIdAsync(key))
            .ReturnsAsync(keyProjection);

        // Act
        var result = await _service.ValidateKeyAsync(key);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("already been used", result.Error);
    }

    [Fact]
    public async Task ValidateKeyAsync_NonExistentKey_ReturnsFailure()
    {
        // Arrange
        var key = "nonexistent_key";
        _mockRepository.Setup(x => x.FindByIdAsync(key))
            .ReturnsAsync((ResetPasswordKeyProjection)null!);

        // Act
        var result = await _service.ValidateKeyAsync(key);

        // Assert
        Assert.True(result.IsFailure);
        Assert.Contains("Invalid reset key", result.Error);
    }

    [Fact]
    public async Task ValidateKeyAsync_NullOrEmptyKey_ReturnsFailure()
    {
        // Act & Assert
        var result1 = await _service.ValidateKeyAsync(null!);
        Assert.True(result1.IsFailure);
        Assert.Contains("null or empty", result1.Error);

        var result2 = await _service.ValidateKeyAsync("");
        Assert.True(result2.IsFailure);
        Assert.Contains("null or empty", result2.Error);

        var result3 = await _service.ValidateKeyAsync("   ");
        Assert.True(result3.IsFailure);
        Assert.Contains("null or empty", result3.Error);
    }
}

#endregion

#region DeleteUser Controller Tests

public class DeleteUserControllerTests
{
    private readonly Mock<ILogger<DeleteUserController>> _mockLogger;
    private readonly Mock<IDeleteUserUseCase> _mockDeleteUserUseCase;
    private readonly DeleteUserController _controller;

    public DeleteUserControllerTests()
    {
        _mockLogger = new Mock<ILogger<DeleteUserController>>();
        _mockDeleteUserUseCase = new Mock<IDeleteUserUseCase>();
        _controller = new DeleteUserController(_mockLogger.Object, _mockDeleteUserUseCase.Object);
    }

    [Fact]
    public async Task DeleteUserAsync_ValidRequest_ReturnsOkResult()
    {
        // Arrange
        var request = new DeleteUserRequest("<EMAIL>", "password123", false);
        _mockDeleteUserUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Success<bool, DeleteUserFailedResponse>(true));

        // Act
        var result = await _controller.DeleteUserAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task DeleteUserAsync_OnlyKeycloakTrue_ReturnsOkResult()
    {
        // Arrange
        var request = new DeleteUserRequest("<EMAIL>", "password123", true);
        _mockDeleteUserUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Success<bool, DeleteUserFailedResponse>(true));

        // Act
        var result = await _controller.DeleteUserAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.True((bool)okResult.Value!);
    }

    [Fact]
    public async Task DeleteUserAsync_NullRequest_ReturnsBadRequest()
    {
        // Act
        var result = await _controller.DeleteUserAsync(null!);

        // Assert
        var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
        Assert.NotNull(badRequestResult.Value);
        Assert.IsType<Error>(badRequestResult.Value);
    }

    [Fact]
    public async Task DeleteUserAsync_UseCaseFailure_ReturnsOkWithError()
    {
        // Arrange
        var request = new DeleteUserRequest("<EMAIL>", "wrongPassword", false);
        var errorResponse = new DeleteUserFailedResponse("invalid_credentials", "Invalid password provided");
        _mockDeleteUserUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Failure<bool, DeleteUserFailedResponse>(errorResponse));

        // Act
        var result = await _controller.DeleteUserAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.Equal(errorResponse, okResult.Value);
    }

    [Fact]
    public async Task DeleteUserAsync_UserNotFound_ReturnsOkWithError()
    {
        // Arrange
        var request = new DeleteUserRequest("<EMAIL>", "password123", false);
        var errorResponse = new DeleteUserFailedResponse("UserDoesNotExist", "User <NAME_EMAIL> does not exist in Keycloak");
        _mockDeleteUserUseCase.Setup(x => x.Process(request))
            .ReturnsAsync(Result.Failure<bool, DeleteUserFailedResponse>(errorResponse));

        // Act
        var result = await _controller.DeleteUserAsync(request);

        // Assert
        var okResult = Assert.IsType<OkObjectResult>(result);
        Assert.Equal(errorResponse, okResult.Value);
    }
}

public class DeleteUserRequestTests
{
    [Fact]
    public void DeleteUserRequest_ValidData_CreatesCorrectly()
    {
        // Arrange & Act
        var request = new DeleteUserRequest("<EMAIL>", "password123", false);

        // Assert
        Assert.Equal("<EMAIL>", request.Email);
        Assert.Equal("password123", request.Password);
        Assert.False(request.OnlyKeycloak);
    }

    [Fact]
    public void DeleteUserRequest_OnlyKeycloakTrue_CreatesCorrectly()
    {
        // Arrange & Act
        var request = new DeleteUserRequest("<EMAIL>", "password123", true);

        // Assert
        Assert.Equal("<EMAIL>", request.Email);
        Assert.Equal("password123", request.Password);
        Assert.True(request.OnlyKeycloak);
    }

    [Fact]
    public void DeleteUserRequest_DefaultOnlyKeycloak_IsFalse()
    {
        // Arrange & Act
        var request = new DeleteUserRequest("<EMAIL>", "password123");

        // Assert
        Assert.False(request.OnlyKeycloak);
    }

    [Fact]
    public void DeleteUserRequest_EmptyEmail_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new DeleteUserRequest("", "password123", false);

        // Assert
        Assert.Equal("", request.Email);
    }

    [Fact]
    public void DeleteUserRequest_NullPassword_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new DeleteUserRequest("<EMAIL>", null!, false);

        // Assert
        Assert.Null(request.Password);
    }

    [Fact]
    public void DeleteUserRequest_WhitespaceEmail_ShouldBeInvalid()
    {
        // Arrange & Act
        var request = new DeleteUserRequest("   ", "password123", false);

        // Assert
        Assert.Equal("   ", request.Email);
    }
}

#endregion
