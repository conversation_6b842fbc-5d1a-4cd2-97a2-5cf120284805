using ITF.Lib.Common.DomainDrivenDesign;

namespace IT.Microservices.AuthenticationApi.ResetPassword;

/// <summary>
/// MongoDB entity for storing password reset keys with expiration and user information
/// </summary>
public class ResetPasswordKeyProjection : BaseClass<string>
{
    public ResetPasswordKeyProjection()
    {
    }

    public ResetPasswordKeyProjection(string key, string email, DateTime expirationTime)
    {
        Id = key;
        Email = email;
        ExpirationTime = expirationTime;
        IsUsed = false;
    }

    /// <summary>
    /// The user's email address associated with this reset key
    /// </summary>
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// When this reset key expires
    /// </summary>
    public DateTime ExpirationTime { get; set; }

    /// <summary>
    /// Whether this key has been used for password reset
    /// </summary>
    public bool IsUsed { get; set; }

    /// <summary>
    /// When this key was used (if applicable)
    /// </summary>
    public DateTime? UsedAt { get; set; }

    /// <summary>
    /// Mark this key as used
    /// </summary>
    public void MarkAsUsed()
    {
        IsUsed = true;
        UsedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Check if this key is valid (not expired and not used)
    /// </summary>
    public bool IsValid()
    {
        return !IsUsed && DateTime.UtcNow <= ExpirationTime;
    }

    /// <summary>
    /// Check if this key has expired
    /// </summary>
    public bool IsExpired()
    {
        return DateTime.UtcNow > ExpirationTime;
    }

    public override void SetId()
    {
        // Id is set in constructor or manually, no auto-generation needed
    }
}
