<?xml version="1.0"?>
<doc>
    <assembly>
        <name>IT.Microservices.AuthenticationApi</name>
    </assembly>
    <members>
        <member name="M:IT.Microservices.AuthenticationApi.Common.KeycloakLoginHttpService.CompleteLogoutAsync(System.String,System.String,System.String)">
            <summary>
            Performs complete logout by revoking tokens AND terminating sessions
            </summary>
        </member>
        <member name="T:IT.Microservices.AuthenticationApi.ResetPassword.ResetPasswordKeyProjection">
            <summary>
            MongoDB entity for storing password reset keys with expiration and user information
            </summary>
        </member>
        <member name="P:IT.Microservices.AuthenticationApi.ResetPassword.ResetPasswordKeyProjection.Email">
            <summary>
            The user's email address associated with this reset key
            </summary>
        </member>
        <member name="P:IT.Microservices.AuthenticationApi.ResetPassword.ResetPasswordKeyProjection.ExpirationTime">
            <summary>
            When this reset key expires
            </summary>
        </member>
        <member name="P:IT.Microservices.AuthenticationApi.ResetPassword.ResetPasswordKeyProjection.IsUsed">
            <summary>
            Whether this key has been used for password reset
            </summary>
        </member>
        <member name="P:IT.Microservices.AuthenticationApi.ResetPassword.ResetPasswordKeyProjection.UsedAt">
            <summary>
            When this key was used (if applicable)
            </summary>
        </member>
        <member name="M:IT.Microservices.AuthenticationApi.ResetPassword.ResetPasswordKeyProjection.MarkAsUsed">
            <summary>
            Mark this key as used
            </summary>
        </member>
        <member name="M:IT.Microservices.AuthenticationApi.ResetPassword.ResetPasswordKeyProjection.IsValid">
            <summary>
            Check if this key is valid (not expired and not used)
            </summary>
        </member>
        <member name="M:IT.Microservices.AuthenticationApi.ResetPassword.ResetPasswordKeyProjection.IsExpired">
            <summary>
            Check if this key has expired
            </summary>
        </member>
        <member name="M:IT.Microservices.AuthenticationApi.ResetPassword.IResetPasswordKeyRepository.FindByEmailAsync(System.String)">
            <summary>
            Find keys by email address
            </summary>
            <param name="email">User's email address</param>
            <returns>Collection of keys for the email</returns>
        </member>
        <member name="M:IT.Microservices.AuthenticationApi.ResetPassword.IResetPasswordKeyService.GenerateResetKeyAsync(System.String)">
            <summary>
            Generates a secure reset key for the given email address
            </summary>
            <param name="email">User's email address</param>
            <returns>A secure reset key</returns>
        </member>
        <member name="M:IT.Microservices.AuthenticationApi.ResetPassword.IResetPasswordKeyService.ValidateAndGetEmailAsync(System.String)">
            <summary>
            Validates a reset key and returns the associated email if valid
            </summary>
            <param name="key">The reset key to validate</param>
            <returns>Result containing the email if valid, or error if invalid/expired</returns>
        </member>
        <member name="M:IT.Microservices.AuthenticationApi.ResetPassword.IResetPasswordKeyService.ValidateKeyAsync(System.String)">
            <summary>
            Validates a reset key without returning the email (for validation-only purposes)
            </summary>
            <param name="key">The reset key to validate</param>
            <returns>Result indicating if the key is valid or error if invalid/expired</returns>
        </member>
        <member name="M:IT.Microservices.AuthenticationApi.ResetPassword.IResetPasswordKeyService.InvalidateKeyAsync(System.String)">
            <summary>
            Invalidates a reset key after successful password reset
            </summary>
            <param name="key">The reset key to invalidate</param>
        </member>
    </members>
</doc>
